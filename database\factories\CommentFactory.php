<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Comment>
 */
class CommentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'content' => $this->faker->paragraph(3),
            'author_name' => $this->faker->name(),
            'author_email' => $this->faker->safeEmail(),
            'author_website' => $this->faker->optional(0.3)->url(),
            'status' => $this->faker->randomElement(['pending', 'approved', 'rejected']),
            'post_id' => \App\Models\Post::factory(),
        ];
    }
}
