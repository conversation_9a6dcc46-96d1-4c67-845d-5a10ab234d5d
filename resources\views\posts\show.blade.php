@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Post Header -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            @if($post->image)
                <img src="{{ $post->image_url }}" alt="{{ $post->title }}" class="w-full h-64 object-cover">
            @endif
            <div class="p-8">
                <div class="flex items-center mb-4">
                    <span class="inline-block px-3 py-1 text-sm font-semibold rounded-full" 
                          style="background-color: {{ $post->category->color }}20; color: {{ $post->category->color }}">
                        {{ $post->category->name }}
                    </span>
                    @if($post->status === 'draft')
                        <span class="ml-2 inline-block px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Draft
                        </span>
                    @endif
                </div>
                
                <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $post->title }}</h1>
                
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center text-sm text-gray-500">
                        <span>By {{ $post->user->name }}</span>
                        <span class="mx-2">•</span>
                        <span>{{ $post->published_at ? $post->published_at->format('M d, Y') : 'Not published' }}</span>
                        @if($post->published_at && $post->created_at->ne($post->updated_at))
                            <span class="mx-2">•</span>
                            <span>Updated {{ $post->updated_at->format('M d, Y') }}</span>
                        @endif
                    </div>
                    
                    @auth
                        @can('update', $post)
                            <div class="flex items-center space-x-2">
                                <a href="{{ route('posts.edit', $post) }}" class="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600">
                                    Edit
                                </a>
                                <form method="POST" action="{{ route('posts.destroy', $post) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this post?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                                        Delete
                                    </button>
                                </form>
                            </div>
                        @endcan
                    @endauth
                </div>
                
                <div class="prose max-w-none">
                    {!! nl2br(e($post->content)) !!}
                </div>
            </div>
        </div>

        <!-- Comments Section -->
        <div class="bg-white rounded-lg shadow-md p-8">
            <h3 class="text-xl font-bold text-gray-900 mb-6">
                Comments ({{ $post->approvedComments->count() }})
            </h3>

            <!-- Comment Form -->
            <div class="mb-8">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Leave a Comment</h4>
                <form method="POST" action="{{ route('comments.store', $post) }}" class="space-y-4">
                    @csrf
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="author_name" class="block text-sm font-medium text-gray-700">Name *</label>
                            <input type="text" name="author_name" id="author_name" required 
                                   value="{{ old('author_name') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('author_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="author_email" class="block text-sm font-medium text-gray-700">Email *</label>
                            <input type="email" name="author_email" id="author_email" required 
                                   value="{{ old('author_email') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('author_email')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    <div>
                        <label for="author_website" class="block text-sm font-medium text-gray-700">Website</label>
                        <input type="url" name="author_website" id="author_website" 
                               value="{{ old('author_website') }}"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        @error('author_website')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700">Comment *</label>
                        <textarea name="content" id="content" rows="4" required 
                                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('content') }}</textarea>
                        @error('content')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                        Submit Comment
                    </button>
                </form>
            </div>

            <!-- Comments List -->
            @if($post->approvedComments->count() > 0)
                <div class="space-y-6">
                    @foreach($post->approvedComments as $comment)
                        <div class="border-l-4 border-indigo-500 pl-4">
                            <div class="flex items-center mb-2">
                                <img src="{{ $comment->gravatar }}" alt="{{ $comment->author_name }}" class="w-8 h-8 rounded-full mr-3">
                                <div>
                                    <h5 class="font-semibold text-gray-900">
                                        @if($comment->author_website)
                                            <a href="{{ $comment->author_website }}" target="_blank" class="hover:text-indigo-600">
                                                {{ $comment->author_name }}
                                            </a>
                                        @else
                                            {{ $comment->author_name }}
                                        @endif
                                    </h5>
                                    <p class="text-sm text-gray-500">{{ $comment->created_at->format('M d, Y \a\t g:i A') }}</p>
                                </div>
                                @auth
                                    <div class="ml-auto flex items-center space-x-2">
                                        <form method="POST" action="{{ route('comments.destroy', $comment) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this comment?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-800 text-sm">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                @endauth
                            </div>
                            <p class="text-gray-700">{{ $comment->content }}</p>
                        </div>
                    @endforeach
                </div>
            @else
                <p class="text-gray-500 text-center py-8">No comments yet. Be the first to comment!</p>
            @endif
        </div>
    </div>
</div>
@endsection
