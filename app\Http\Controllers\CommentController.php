<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Post;
use Illuminate\Http\Request;

class CommentController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Post $post)
    {
        $validated = $request->validate([
            'content' => 'required|string|max:1000',
            'author_name' => 'required|string|max:255',
            'author_email' => 'required|email|max:255',
            'author_website' => 'nullable|url|max:255',
        ]);

        $validated['post_id'] = $post->id;
        $validated['status'] = 'pending'; // Comments need approval

        Comment::create($validated);

        return redirect()->route('posts.show', $post)
            ->with('success', 'Comment submitted successfully! It will be visible after approval.');
    }

    /**
     * Update the specified resource in storage (for admin approval).
     */
    public function update(Request $request, Comment $comment)
    {
        $this->middleware('auth');

        $validated = $request->validate([
            'status' => 'required|in:pending,approved,rejected',
        ]);

        $comment->update($validated);

        return redirect()->back()
            ->with('success', 'Comment status updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Comment $comment)
    {
        $this->middleware('auth');

        $comment->delete();

        return redirect()->back()
            ->with('success', 'Comment deleted successfully!');
    }
}
