@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Categories</h1>
            @auth
                <a href="{{ route('categories.create') }}" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    Create New Category
                </a>
            @endauth
        </div>

        @if($categories->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($categories as $category)
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-4 h-4 rounded-full mr-3" style="background-color: {{ $category->color }}"></div>
                                <h3 class="text-lg font-semibold text-gray-900">
                                    <a href="{{ route('categories.show', $category) }}" class="hover:text-indigo-600">
                                        {{ $category->name }}
                                    </a>
                                </h3>
                            </div>
                            <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                {{ $category->posts_count }} {{ $category->posts_count == 1 ? 'post' : 'posts' }}
                            </span>
                        </div>
                        
                        @if($category->description)
                            <p class="text-gray-600 text-sm mb-4">{{ $category->description }}</p>
                        @endif
                        
                        <div class="flex items-center justify-between">
                            <a href="{{ route('categories.show', $category) }}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                                View Posts →
                            </a>
                            @auth
                                <div class="flex items-center space-x-2">
                                    <a href="{{ route('categories.edit', $category) }}" class="text-yellow-600 hover:text-yellow-800 text-sm font-medium">
                                        Edit
                                    </a>
                                    @if($category->posts_count == 0)
                                        <form method="POST" action="{{ route('categories.destroy', $category) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this category?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="text-red-600 hover:text-red-800 text-sm font-medium">
                                                Delete
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            @endauth
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-12">
                <div class="text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No categories found</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating a new category.</p>
                    @auth
                        <div class="mt-6">
                            <a href="{{ route('categories.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Create your first category
                            </a>
                        </div>
                    @endauth
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
