<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Blog Management System</title>
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="font-sans antialiased bg-gray-50">
    <div class="min-h-screen">
        <!-- Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-xl font-bold text-gray-900">Blog Management System</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="<?php echo e(route('login')); ?>" class="text-gray-600 hover:text-gray-900">Login</a>
                        <a href="<?php echo e(route('register')); ?>" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">Register</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Hero Section -->
        <div class="bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
                <div class="text-center">
                    <h1 class="text-4xl font-bold text-gray-900 sm:text-5xl md:text-6xl">
                        Welcome to Your Blog
                    </h1>
                    <p class="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
                        A complete blog management system with posts, categories, comments, and user authentication.
                    </p>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="py-12 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="lg:text-center">
                    <h2 class="text-base text-indigo-600 font-semibold tracking-wide uppercase">Features</h2>
                    <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
                        Everything you need for blogging
                    </p>
                </div>

                <div class="mt-10">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                        <!-- Post Management -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="text-indigo-600 mb-4">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900">Post Management</h3>
                            <p class="mt-2 text-sm text-gray-500">Create, edit, and delete blog posts with rich content and images.</p>
                        </div>

                        <!-- Category System -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="text-indigo-600 mb-4">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900">Categories</h3>
                            <p class="mt-2 text-sm text-gray-500">Organize posts with color-coded categories and filtering.</p>
                        </div>

                        <!-- User Authentication -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="text-indigo-600 mb-4">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900">User Authentication</h3>
                            <p class="mt-2 text-sm text-gray-500">Secure login system with user permissions and authorization.</p>
                        </div>

                        <!-- Comments -->
                        <div class="bg-white rounded-lg shadow p-6">
                            <div class="text-indigo-600 mb-4">
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900">Comments</h3>
                            <p class="mt-2 text-sm text-gray-500">Interactive comment system with moderation and approval.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Setup Notice -->
        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mx-4 my-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-yellow-700">
                        <strong>Database Setup Required:</strong> To use the full blog functionality, please set up your database connection. 
                        <br>
                        <strong>For SQLite:</strong> Enable the SQLite extension in your PHP configuration file at <code>C:\php-8.4.5\php.ini</code>
                        <br>
                        <strong>For MySQL:</strong> Install XAMPP and configure the database connection in your <code>.env</code> file.
                    </p>
                </div>
            </div>
        </div>

        <!-- Getting Started -->
        <div class="bg-white py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <h2 class="text-3xl font-extrabold text-gray-900">Getting Started</h2>
                    <p class="mt-4 text-lg text-gray-500">Follow these steps to set up your blog:</p>
                </div>
                
                <div class="mt-10 grid grid-cols-1 md:grid-cols-3 gap-8">
                    <div class="text-center">
                        <div class="bg-indigo-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                            <span class="text-indigo-600 font-bold">1</span>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900">Setup Database</h3>
                        <p class="mt-2 text-sm text-gray-500">Configure SQLite or MySQL database connection</p>
                    </div>
                    
                    <div class="text-center">
                        <div class="bg-indigo-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                            <span class="text-indigo-600 font-bold">2</span>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900">Run Migrations</h3>
                        <p class="mt-2 text-sm text-gray-500">Execute <code>php artisan migrate:fresh --seed</code></p>
                    </div>
                    
                    <div class="text-center">
                        <div class="bg-indigo-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-4">
                            <span class="text-indigo-600 font-bold">3</span>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900">Start Blogging</h3>
                        <p class="mt-2 text-sm text-gray-500">Register an account and start creating content</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <footer class="bg-gray-800">
            <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
                <p class="text-center text-gray-400">
                    Blog Management System - Built with Laravel 12 & Tailwind CSS
                </p>
            </div>
        </footer>
    </div>
</body>
</html>
<?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel\final-project\resources\views/welcome-blog.blade.php ENDPATH**/ ?>