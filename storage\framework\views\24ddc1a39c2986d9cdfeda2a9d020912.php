<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Post Header -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <?php if($post->image): ?>
                <img src="<?php echo e($post->image_url); ?>" alt="<?php echo e($post->title); ?>" class="w-full h-64 object-cover">
            <?php endif; ?>
            <div class="p-8">
                <div class="flex items-center mb-4">
                    <span class="inline-block px-3 py-1 text-sm font-semibold rounded-full" 
                          style="background-color: <?php echo e($post->category->color); ?>20; color: <?php echo e($post->category->color); ?>">
                        <?php echo e($post->category->name); ?>

                    </span>
                    <?php if($post->status === 'draft'): ?>
                        <span class="ml-2 inline-block px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Draft
                        </span>
                    <?php endif; ?>
                </div>
                
                <h1 class="text-3xl font-bold text-gray-900 mb-4"><?php echo e($post->title); ?></h1>
                
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center text-sm text-gray-500">
                        <span>By <?php echo e($post->user->name); ?></span>
                        <span class="mx-2">•</span>
                        <span><?php echo e($post->published_at ? $post->published_at->format('M d, Y') : 'Not published'); ?></span>
                        <?php if($post->published_at && $post->created_at->ne($post->updated_at)): ?>
                            <span class="mx-2">•</span>
                            <span>Updated <?php echo e($post->updated_at->format('M d, Y')); ?></span>
                        <?php endif; ?>
                    </div>
                    
                    <?php if(auth()->guard()->check()): ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $post)): ?>
                            <div class="flex items-center space-x-2">
                                <a href="<?php echo e(route('posts.edit', $post)); ?>" class="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600">
                                    Edit
                                </a>
                                <form method="POST" action="<?php echo e(route('posts.destroy', $post)); ?>" class="inline" onsubmit="return confirm('Are you sure you want to delete this post?')">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                                        Delete
                                    </button>
                                </form>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                
                <div class="prose max-w-none">
                    <?php echo nl2br(e($post->content)); ?>

                </div>
            </div>
        </div>

        <!-- Comments Section -->
        <div class="bg-white rounded-lg shadow-md p-8">
            <h3 class="text-xl font-bold text-gray-900 mb-6">
                Comments (<?php echo e($post->approvedComments->count()); ?>)
            </h3>

            <!-- Comment Form -->
            <div class="mb-8">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Leave a Comment</h4>
                <form method="POST" action="<?php echo e(route('comments.store', $post)); ?>" class="space-y-4">
                    <?php echo csrf_field(); ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="author_name" class="block text-sm font-medium text-gray-700">Name *</label>
                            <input type="text" name="author_name" id="author_name" required 
                                   value="<?php echo e(old('author_name')); ?>"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <?php $__errorArgs = ['author_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <div>
                            <label for="author_email" class="block text-sm font-medium text-gray-700">Email *</label>
                            <input type="email" name="author_email" id="author_email" required 
                                   value="<?php echo e(old('author_email')); ?>"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <?php $__errorArgs = ['author_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div>
                        <label for="author_website" class="block text-sm font-medium text-gray-700">Website</label>
                        <input type="url" name="author_website" id="author_website" 
                               value="<?php echo e(old('author_website')); ?>"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <?php $__errorArgs = ['author_website'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700">Comment *</label>
                        <textarea name="content" id="content" rows="4" required 
                                  class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"><?php echo e(old('content')); ?></textarea>
                        <?php $__errorArgs = ['content'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <button type="submit" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                        Submit Comment
                    </button>
                </form>
            </div>

            <!-- Comments List -->
            <?php if($post->approvedComments->count() > 0): ?>
                <div class="space-y-6">
                    <?php $__currentLoopData = $post->approvedComments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $comment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border-l-4 border-indigo-500 pl-4">
                            <div class="flex items-center mb-2">
                                <img src="<?php echo e($comment->gravatar); ?>" alt="<?php echo e($comment->author_name); ?>" class="w-8 h-8 rounded-full mr-3">
                                <div>
                                    <h5 class="font-semibold text-gray-900">
                                        <?php if($comment->author_website): ?>
                                            <a href="<?php echo e($comment->author_website); ?>" target="_blank" class="hover:text-indigo-600">
                                                <?php echo e($comment->author_name); ?>

                                            </a>
                                        <?php else: ?>
                                            <?php echo e($comment->author_name); ?>

                                        <?php endif; ?>
                                    </h5>
                                    <p class="text-sm text-gray-500"><?php echo e($comment->created_at->format('M d, Y \a\t g:i A')); ?></p>
                                </div>
                                <?php if(auth()->guard()->check()): ?>
                                    <div class="ml-auto flex items-center space-x-2">
                                        <form method="POST" action="<?php echo e(route('comments.destroy', $comment)); ?>" class="inline" onsubmit="return confirm('Are you sure you want to delete this comment?')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="text-red-600 hover:text-red-800 text-sm">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <p class="text-gray-700"><?php echo e($comment->content); ?></p>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <p class="text-gray-500 text-center py-8">No comments yet. Be the first to comment!</p>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel\final-project\resources\views/posts/show.blade.php ENDPATH**/ ?>