<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Category;
use App\Models\Post;
use App\Models\Comment;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BlogSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test user
        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Create additional users
        $users = User::factory(5)->create();
        $allUsers = $users->push($user);

        // Create categories
        $categories = Category::factory(8)->create();

        // Create posts
        $posts = collect();
        foreach ($allUsers as $user) {
            $userPosts = Post::factory(rand(2, 5))->create([
                'user_id' => $user->id,
                'category_id' => $categories->random()->id,
            ]);
            $posts = $posts->merge($userPosts);
        }

        // Create comments for posts
        foreach ($posts as $post) {
            Comment::factory(rand(0, 8))->create([
                'post_id' => $post->id,
            ]);
        }

        $this->command->info('Blog data seeded successfully!');
        $this->command->info("Created {$allUsers->count()} users, {$categories->count()} categories, {$posts->count()} posts");
    }
}
