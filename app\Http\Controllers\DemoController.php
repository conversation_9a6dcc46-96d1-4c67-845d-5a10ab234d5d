<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class DemoController extends Controller
{
    public function home()
    {
        // Mock data for demonstration
        $posts = collect([
            (object) [
                'id' => 1,
                'title' => 'Welcome to Our Blog',
                'slug' => 'welcome-to-our-blog',
                'excerpt' => 'This is a sample blog post to demonstrate the blog management system features.',
                'content' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
                'image_url' => null,
                'published_at' => now(),
                'user' => (object) ['name' => 'John Doe'],
                'category' => (object) ['name' => 'Technology', 'color' => '#3B82F6', 'slug' => 'technology']
            ],
            (object) [
                'id' => 2,
                'title' => 'Getting Started with Laravel',
                'slug' => 'getting-started-with-laravel',
                'excerpt' => 'Learn how to build amazing web applications with Laravel framework.',
                'content' => 'Laravel is a web application framework with expressive, elegant syntax.',
                'image_url' => null,
                'published_at' => now()->subDays(1),
                'user' => (object) ['name' => '<PERSON>'],
                'category' => (object) ['name' => 'Programming', 'color' => '#10B981', 'slug' => 'programming']
            ],
            (object) [
                'id' => 3,
                'title' => 'Building Modern UIs with Tailwind CSS',
                'slug' => 'building-modern-uis-with-tailwind-css',
                'excerpt' => 'Discover how to create beautiful, responsive designs with Tailwind CSS.',
                'content' => 'Tailwind CSS is a utility-first CSS framework for rapidly building custom user interfaces.',
                'image_url' => null,
                'published_at' => now()->subDays(2),
                'user' => (object) ['name' => 'Mike Johnson'],
                'category' => (object) ['name' => 'Design', 'color' => '#F59E0B', 'slug' => 'design']
            ]
        ]);

        $categories = collect([
            (object) ['name' => 'Technology', 'color' => '#3B82F6', 'slug' => 'technology', 'posts_count' => 5],
            (object) ['name' => 'Programming', 'color' => '#10B981', 'slug' => 'programming', 'posts_count' => 3],
            (object) ['name' => 'Design', 'color' => '#F59E0B', 'slug' => 'design', 'posts_count' => 2],
            (object) ['name' => 'Business', 'color' => '#8B5CF6', 'slug' => 'business', 'posts_count' => 1]
        ]);

        $featuredPosts = $posts->take(3);

        // Convert to paginated-like object
        $posts = new \Illuminate\Pagination\LengthAwarePaginator(
            $posts,
            $posts->count(),
            6,
            1,
            ['path' => request()->url()]
        );

        return view('home', compact('posts', 'categories', 'featuredPosts'));
    }

    public function posts()
    {
        $posts = collect([
            (object) [
                'id' => 1,
                'title' => 'Welcome to Our Blog',
                'slug' => 'welcome-to-our-blog',
                'excerpt' => 'This is a sample blog post to demonstrate the blog management system features.',
                'image_url' => null,
                'published_at' => now(),
                'user' => (object) ['name' => 'John Doe'],
                'category' => (object) ['name' => 'Technology', 'color' => '#3B82F6']
            ],
            (object) [
                'id' => 2,
                'title' => 'Getting Started with Laravel',
                'slug' => 'getting-started-with-laravel',
                'excerpt' => 'Learn how to build amazing web applications with Laravel framework.',
                'image_url' => null,
                'published_at' => now()->subDays(1),
                'user' => (object) ['name' => 'Jane Smith'],
                'category' => (object) ['name' => 'Programming', 'color' => '#10B981']
            ]
        ]);

        $posts = new \Illuminate\Pagination\LengthAwarePaginator(
            $posts,
            $posts->count(),
            10,
            1,
            ['path' => request()->url()]
        );

        return view('posts.index', compact('posts'));
    }

    public function categories()
    {
        $categories = collect([
            (object) ['name' => 'Technology', 'color' => '#3B82F6', 'slug' => 'technology', 'posts_count' => 5, 'description' => 'Latest technology trends and news'],
            (object) ['name' => 'Programming', 'color' => '#10B981', 'slug' => 'programming', 'posts_count' => 3, 'description' => 'Programming tutorials and tips'],
            (object) ['name' => 'Design', 'color' => '#F59E0B', 'slug' => 'design', 'posts_count' => 2, 'description' => 'UI/UX design and inspiration'],
            (object) ['name' => 'Business', 'color' => '#8B5CF6', 'slug' => 'business', 'posts_count' => 1, 'description' => 'Business strategies and insights']
        ]);

        return view('categories.index', compact('categories'));
    }
}
