@extends('layouts.app')

@section('content')
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Category Header -->
        <div class="bg-white rounded-lg shadow-md p-8 mb-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-6 h-6 rounded-full mr-4" style="background-color: {{ $category->color }}"></div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">{{ $category->name }}</h1>
                        @if($category->description)
                            <p class="text-gray-600 mt-2">{{ $category->description }}</p>
                        @endif
                        <p class="text-sm text-gray-500 mt-1">
                            {{ $posts->total() }} {{ $posts->total() == 1 ? 'post' : 'posts' }} in this category
                        </p>
                    </div>
                </div>
                @auth
                    <div class="flex items-center space-x-2">
                        <a href="{{ route('categories.edit', $category) }}" class="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600">
                            Edit Category
                        </a>
                        @if($posts->total() == 0)
                            <form method="POST" action="{{ route('categories.destroy', $category) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this category?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">
                                    Delete
                                </button>
                            </form>
                        @endif
                    </div>
                @endauth
            </div>
        </div>

        <!-- Posts in Category -->
        @if($posts->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($posts as $post)
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    @if($post->image)
                        <img src="{{ $post->image_url }}" alt="{{ $post->title }}" class="w-full h-48 object-cover">
                    @endif
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <a href="{{ route('posts.show', $post) }}" class="hover:text-indigo-600">
                                {{ $post->title }}
                            </a>
                        </h3>
                        <p class="text-gray-600 text-sm mb-4">{{ $post->excerpt }}</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <span>By {{ $post->user->name }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ $post->published_at->format('M d, Y') }}</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="{{ route('posts.show', $post) }}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                                    Read more
                                </a>
                                @auth
                                    @can('update', $post)
                                        <a href="{{ route('posts.edit', $post) }}" class="text-yellow-600 hover:text-yellow-800 text-sm font-medium">
                                            Edit
                                        </a>
                                    @endcan
                                @endauth
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $posts->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No posts in this category</h3>
                    <p class="mt-1 text-sm text-gray-500">Be the first to create a post in this category.</p>
                    @auth
                        <div class="mt-6">
                            <a href="{{ route('posts.create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Create a post
                            </a>
                        </div>
                    @endauth
                </div>
            </div>
        @endif
    </div>
</div>
@endsection
