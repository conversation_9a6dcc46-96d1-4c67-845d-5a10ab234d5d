<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900">All Posts</h1>
            <?php if(auth()->guard()->check()): ?>
                <a href="<?php echo e(route('posts.create')); ?>" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                    Create New Post
                </a>
            <?php endif; ?>
        </div>

        <?php if($posts->count() > 0): ?>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <?php if($post->image): ?>
                        <img src="<?php echo e($post->image_url); ?>" alt="<?php echo e($post->title); ?>" class="w-full h-48 object-cover">
                    <?php endif; ?>
                    <div class="p-6">
                        <div class="flex items-center mb-2">
                            <span class="inline-block px-2 py-1 text-xs font-semibold rounded-full" 
                                  style="background-color: <?php echo e($post->category->color); ?>20; color: <?php echo e($post->category->color); ?>">
                                <?php echo e($post->category->name); ?>

                            </span>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <a href="<?php echo e(route('posts.show', $post)); ?>" class="hover:text-indigo-600">
                                <?php echo e($post->title); ?>

                            </a>
                        </h3>
                        <p class="text-gray-600 text-sm mb-4"><?php echo e($post->excerpt); ?></p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <span>By <?php echo e($post->user->name); ?></span>
                                <span class="mx-2">•</span>
                                <span><?php echo e($post->published_at->format('M d, Y')); ?></span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="<?php echo e(route('posts.show', $post)); ?>" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                                    Read more
                                </a>
                                <?php if(auth()->guard()->check()): ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $post)): ?>
                                        <a href="<?php echo e(route('posts.edit', $post)); ?>" class="text-yellow-600 hover:text-yellow-800 text-sm font-medium">
                                            Edit
                                        </a>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                <?php echo e($posts->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <div class="text-gray-500">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No posts found</h3>
                    <p class="mt-1 text-sm text-gray-500">Get started by creating a new post.</p>
                    <?php if(auth()->guard()->check()): ?>
                        <div class="mt-6">
                            <a href="<?php echo e(route('posts.create')); ?>" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Create your first post
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Desktop\laravel\final-project\resources\views/posts/index.blade.php ENDPATH**/ ?>